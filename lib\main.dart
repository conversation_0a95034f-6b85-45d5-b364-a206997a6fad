import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'theme/app_theme.dart';
import 'providers/theme_provider.dart';
import 'screens/home_screen.dart';
import 'screens/video_player_screen.dart';
import 'services/deeplink_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  final prefs = await SharedPreferences.getInstance();

  runApp(MyApp(prefs: prefs));
}

class MyApp extends StatefulWidget {
  final SharedPreferences prefs;

  const MyApp({super.key, required this.prefs});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();

  @override
  void initState() {
    super.initState();
    _initializeDeeplinks();
  }

  void _initializeDeeplinks() async {
    debugPrint('MyApp: Initializing deeplink service');
    // Initialize the service first to create the stream
    await DeeplinkService.instance.initialize();

    debugPrint('MyApp: Setting up deeplink listener');
    // Get the stream (it's guaranteed to be non-null now)
    final stream = DeeplinkService.instance.deeplinkStream;

    stream?.listen((presetItem) {
      debugPrint('MyApp: Received deeplink preset item: ${presetItem.name}');
      // Navigate to video player screen
      final navigator = _navigatorKey.currentState;
      if (navigator != null) {
        debugPrint('MyApp: Navigating to video player screen');
        navigator.push(
          MaterialPageRoute(
            builder: (context) => VideoPlayerScreen(videoItem: presetItem),
          ),
        );
      } else {
        debugPrint('MyApp: Navigator is null, cannot navigate');
      }
    });

    // Notify the service that the listener is ready to process any pending links
    debugPrint('MyApp: Notifying service that listener is ready');
    DeeplinkService.instance.notifyListenerReady();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => ThemeProvider(widget.prefs),
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            navigatorKey: _navigatorKey,
            title: 'Cat Player',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeProvider.themeMode,
            home: const HomeScreen(),
          );
        },
      ),
    );
  }
}
