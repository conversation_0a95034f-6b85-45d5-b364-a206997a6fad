import 'package:flutter/material.dart';
import 'package:better_player/better_player.dart';
import '../controllers/custom_player_controller.dart';

/// A shared video player widget that can be used in both normal and PiP modes
/// This ensures the same BetterPlayer instance is used to avoid disposal issues
class SharedVideoPlayer extends StatelessWidget {
  final CustomPlayerController controller;
  final String playerId;

  const SharedVideoPlayer({
    super.key,
    required this.controller,
    required this.playerId,
  });

  @override
  Widget build(BuildContext context) {
    if (controller.betterPlayerController == null) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return BetterPlayer(
      controller: controller.betterPlayerController!,
      key: Value<PERSON><PERSON>('shared_video_player_$playerId'),
    );
  }
}
