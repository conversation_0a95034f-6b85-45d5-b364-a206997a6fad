import 'video_source.dart';

class PresetItem {
  final String id;
  final String name;
  final List<VideoSource> sources;
  final DateTime dateAdded;

  PresetItem({
    required this.id,
    required this.name,
    required this.sources,
    DateTime? dateAdded,
  }) : dateAdded = dateAdded ?? DateTime.now();

  /// Constructor for backward compatibility with single URL
  PresetItem.single({
    required this.id,
    required this.name,
    required String url,
    String? agent,
    String? referer,
    DateTime? dateAdded,
  })  : sources = [
          VideoSource(
            url: url,
            userAgent: agent,
            referer: referer,
            priority: 0,
          )
        ],
        dateAdded = dateAdded ?? DateTime.now();

  PresetItem copyWith({
    String? name,
    List<VideoSource>? sources,
  }) {
    return PresetItem(
      id: id,
      name: name ?? this.name,
      sources: sources ?? this.sources,
      dateAdded: dateAdded,
    );
  }

  /// Backward compatibility - get primary URL
  String get url => sources.isNotEmpty ? sources.first.url : '';

  /// Backward compatibility - get primary user agent
  String? get agent => sources.isNotEmpty ? sources.first.userAgent : null;

  /// Backward compatibility - get primary referer
  String? get referer => sources.isNotEmpty ? sources.first.referer : null;

  /// Get primary source (first source with highest priority)
  VideoSource? get primarySource {
    if (sources.isEmpty) return null;
    final sortedSources = List<VideoSource>.from(sources)
      ..sort((a, b) => a.priority.compareTo(b.priority));
    return sortedSources.first;
  }

  /// Get all sources sorted by priority
  List<VideoSource> get sortedSources {
    final sorted = List<VideoSource>.from(sources)
      ..sort((a, b) => a.priority.compareTo(b.priority));
    return sorted;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PresetItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PresetItem(id: $id, name: $name, url: $url)';
  }
}
