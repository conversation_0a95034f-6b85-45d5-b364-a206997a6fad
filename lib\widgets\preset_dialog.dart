import 'package:cat_player/models/preset_item.dart';
import 'package:flutter/material.dart';

class PresetDialog extends StatefulWidget {
  final PresetItem? preset;
  final Function(PresetItem) onSave;

  const PresetDialog({super.key, this.preset, required this.onSave});

  @override
  State<PresetDialog> createState() => _PresetDialogState();
}

class _PresetDialogState extends State<PresetDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _urlController;
  late TextEditingController _agentController;
  late TextEditingController _refererController;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.preset?.name);
    _urlController = TextEditingController(text: widget.preset?.url);
    _agentController = TextEditingController(text: widget.preset?.agent);
    _refererController = TextEditingController(text: widget.preset?.referer);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _urlController.dispose();
    _agentController.dispose();
    _refererController.dispose();
    super.dispose();
  }

  void _save() {
    if (_formKey.currentState!.validate()) {
      final newPreset = PresetItem.single(
        id: widget.preset?.id ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text.trim(),
        url: _urlController.text.trim(),
        agent: _agentController.text.trim(),
        referer: _refererController.text.trim(),
      );
      widget.onSave(newPreset);
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.preset == null ? 'Add New Preset' : 'Edit Preset'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8, // 80% of screen width
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Name',
                  hintText: 'Enter preset name',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Name is required';
                  }
                  return null;
                },
                autovalidateMode: AutovalidateMode.onUserInteraction,
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _urlController,
                decoration: const InputDecoration(
                  labelText: 'URL',
                  hintText: 'Enter preset URL',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'URL is required';
                  }
                  // Basic URL validation
                  final uri = Uri.tryParse(value);
                  if (uri == null || !uri.isAbsolute) {
                    return 'Enter a valid URL';
                  }
                  return null;
                },
                autovalidateMode: AutovalidateMode.onUserInteraction,
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _agentController,
                decoration:
                    const InputDecoration(labelText: 'Agent (Optional)'),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _refererController,
                decoration:
                    const InputDecoration(labelText: 'Referer (Optional)'),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _save,
          child: Text(widget.preset == null ? 'Add' : 'Save'),
        ),
      ],
    );
  }
}
