import 'package:flutter/material.dart';
import 'package:floating/floating.dart';
import '../controllers/custom_player_controller.dart';

/// A widget that switches between different layouts based on PiP status
class CustomPiPSwitcher extends StatefulWidget {
  final CustomPlayerController controller;
  final Widget childWhenDisabled;
  final Widget childWhenEnabled;

  const CustomPiPSwitcher({
    super.key,
    required this.controller,
    required this.childWhenDisabled,
    required this.childWhenEnabled,
  });

  @override
  State<CustomPiPSwitcher> createState() => _CustomPiPSwitcherState();
}

class _CustomPiPSwitcherState extends State<CustomPiPSwitcher> {
  late Floating _floating;
  PiPStatus _currentPipStatus = PiPStatus.disabled;

  @override
  void initState() {
    super.initState();
    _floating = Floating();
    // Initialize PiP functionality
    widget.controller.initializePip();

    // Start monitoring PiP status
    _monitorPipStatus();
  }

  void _monitorPipStatus() async {
    try {
      final status = await _floating.pipStatus;
      if (mounted && status != _currentPipStatus) {
        setState(() {
          _currentPipStatus = status;
        });

        // Update controller status
        widget.controller.updatePipStatus();
      }
    } catch (e) {
      debugPrint('Error monitoring PiP status: $e');
    }

    // Continue monitoring
    if (mounted) {
      Future.delayed(const Duration(milliseconds: 500), _monitorPipStatus);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Use our own logic instead of the floating package's PiPSwitcher
    // to avoid widget disposal issues
    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, child) {
        final isPipMode = _currentPipStatus == PiPStatus.enabled;

        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: isPipMode ? widget.childWhenEnabled : widget.childWhenDisabled,
        );
      },
    );
  }
}
