import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/preset_item.dart';
import '../models/video_source.dart';

/// Service to handle deeplink parsing and navigation
class DeeplinkService {
  static const MethodChannel _channel = MethodChannel('cat_player/deeplink');
  static DeeplinkService? _instance;

  StreamController<PresetItem>? _deeplinkController;
  Stream<PresetItem>? _deeplinkStream;
  final List<PresetItem> _pendingDeeplinks = [];

  DeeplinkService._();

  static DeeplinkService get instance {
    _instance ??= DeeplinkService._();
    return _instance!;
  }

  /// Initialize the deeplink service
  Future<void> initialize() async {
    debugPrint('DeeplinkService: Initializing service');
    _deeplinkController = StreamController<PresetItem>.broadcast();
    _deeplinkStream = _deeplinkController!.stream;

    // Set up method channel handler
    _channel.setMethodCallHandler(_handleMethodCall);

    // Add a small delay to ensure Flutter app is fully ready
    await Future.delayed(const Duration(milliseconds: 100));

    // Check for initial deeplink when app starts
    await _checkInitialDeeplink();
  }

  /// Get the deeplink stream
  Stream<PresetItem>? get deeplinkStream => _deeplinkStream;

  /// Notify that a listener has been set up
  void notifyListenerReady() {
    debugPrint('DeeplinkService: Listener ready, processing pending deeplinks');
    _processPendingDeeplinks();
  }

  /// Handle method calls from native side
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    debugPrint('DeeplinkService: Received method call: ${call.method}');
    switch (call.method) {
      case 'handleDeeplink':
        final String? url = call.arguments as String?;
        debugPrint('DeeplinkService: Handling deeplink: $url');
        if (url != null) {
          final presetItem = parseDeeplink(url);
          if (presetItem != null) {
            debugPrint(
                'DeeplinkService: Parsed preset item: ${presetItem.name}');
            _addDeeplink(presetItem);
          } else {
            debugPrint('DeeplinkService: Failed to parse deeplink');
          }
        }
        break;
      default:
        throw PlatformException(
          code: 'Unimplemented',
          details: 'Method ${call.method} not implemented',
        );
    }
  }

  /// Add deeplink to stream or queue if stream is not ready
  void _addDeeplink(PresetItem presetItem) {
    // Queue the deeplink if the controller is not ready or has no listener
    if (_deeplinkController == null || !_deeplinkController!.hasListener) {
      debugPrint(
          'DeeplinkService: Controller not ready or no listener, queueing deeplink for later');
      _pendingDeeplinks.add(presetItem);
    } else {
      debugPrint('DeeplinkService: Adding deeplink directly to stream');
      _deeplinkController!.add(presetItem);
    }
  }

  /// Process any pending deeplinks when listener is ready
  void _processPendingDeeplinks() {
    if (_pendingDeeplinks.isNotEmpty && _deeplinkController != null) {
      debugPrint(
          'DeeplinkService: Processing ${_pendingDeeplinks.length} pending deeplinks');
      for (final presetItem in _pendingDeeplinks) {
        _deeplinkController!.add(presetItem);
      }
      _pendingDeeplinks.clear();
    }
  }

  /// Check for initial deeplink when app starts
  Future<void> _checkInitialDeeplink() async {
    try {
      debugPrint('DeeplinkService: Checking for initial deeplink');
      final String? initialUrl =
          await _channel.invokeMethod('getInitialDeeplink');
      debugPrint('DeeplinkService: Initial deeplink URL: $initialUrl');
      if (initialUrl != null) {
        final presetItem = parseDeeplink(initialUrl);
        if (presetItem != null) {
          debugPrint(
              'DeeplinkService: Parsed initial preset item: ${presetItem.name}');
          // Delay to ensure the app is fully initialized
          Future.delayed(const Duration(milliseconds: 1500), () {
            debugPrint('DeeplinkService: Adding initial preset item to stream');
            _deeplinkController?.add(presetItem);
          });
        }
      }
    } catch (e) {
      debugPrint('Error getting initial deeplink: $e');
    }
  }

  /// Parse deeplink URL and create PresetItem
  /// Expected format: catplayer://play?name=value&src1=link&src2=link2&userAgent=Agent&referer=referer
  PresetItem? parseDeeplink(String url) {
    try {
      debugPrint('DeeplinkService: Full URL to parse: $url');
      final uri = Uri.parse(url);
      debugPrint(
          'DeeplinkService: Parsed URI - scheme: ${uri.scheme}, host: ${uri.host}');
      debugPrint('DeeplinkService: Query parameters: ${uri.queryParameters}');

      // Check if it's a valid catplayer scheme
      if (uri.scheme != 'catplayer') {
        debugPrint('Invalid scheme: ${uri.scheme}');
        return null;
      }

      // Check if it's a play action
      if (uri.host != 'play') {
        debugPrint('Invalid host/action: ${uri.host}');
        return null;
      }

      final params = uri.queryParameters;

      // Name is required
      final name = params['name'];
      if (name == null || name.isEmpty) {
        debugPrint('Name parameter is required');
        return null;
      }

      // Extract sources (src1, src2, src3, etc.)
      final sources = <VideoSource>[];
      int sourceIndex = 1;

      while (true) {
        final srcKey = sourceIndex == 1 ? 'src1' : 'src$sourceIndex';
        final srcUrl = params[srcKey];

        if (srcUrl == null || srcUrl.isEmpty) {
          break;
        }

        sources.add(VideoSource(
          url: srcUrl,
          userAgent: params['userAgent'],
          referer: params['referer'],
          priority: sourceIndex - 1,
          label: 'Source $sourceIndex',
        ));

        sourceIndex++;
      }

      // At least one source is required
      if (sources.isEmpty) {
        debugPrint('At least one source is required');
        return null;
      }

      return PresetItem(
        id: 'deeplink_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        sources: sources,
      );
    } catch (e) {
      debugPrint('Error parsing deeplink: $e');
      return null;
    }
  }

  /// Create a deeplink URL from PresetItem
  String createDeeplinkUrl(PresetItem item) {
    final uri = Uri(
      scheme: 'catplayer',
      host: 'play',
      queryParameters: {
        'name': item.name,
        ...{
          for (int i = 0; i < item.sources.length; i++)
            'src${i + 1}': item.sources[i].url,
        },
        if (item.sources.isNotEmpty && item.sources.first.userAgent != null)
          'userAgent': item.sources.first.userAgent!,
        if (item.sources.isNotEmpty && item.sources.first.referer != null)
          'referer': item.sources.first.referer!,
      },
    );
    return uri.toString();
  }

  /// Dispose the service
  void dispose() {
    _deeplinkController?.close();
    _deeplinkController = null;
    _deeplinkStream = null;
  }
}
