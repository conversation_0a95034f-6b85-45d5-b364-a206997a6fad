/// Represents a video source with URL and optional headers
class VideoSource {
  final String url;
  final String? userAgent;
  final String? referer;
  final int priority; // Lower number = higher priority
  final String? label; // Optional label for the source (e.g., "Source 1", "Backup")

  const VideoSource({
    required this.url,
    this.userAgent,
    this.referer,
    this.priority = 0,
    this.label,
  });

  VideoSource copyWith({
    String? url,
    String? userAgent,
    String? referer,
    int? priority,
    String? label,
  }) {
    return VideoSource(
      url: url ?? this.url,
      userAgent: userAgent ?? this.userAgent,
      referer: referer ?? this.referer,
      priority: priority ?? this.priority,
      label: label ?? this.label,
    );
  }

  /// Create VideoSource from URL query parameters
  factory VideoSource.fromQueryParams(Map<String, String> params, int priority) {
    return VideoSource(
      url: params['src'] ?? params['url'] ?? '',
      userAgent: params['userAgent'],
      referer: params['referer'],
      priority: priority,
      label: params['label'],
    );
  }

  /// Convert to map for serialization
  Map<String, dynamic> toMap() {
    return {
      'url': url,
      'userAgent': userAgent,
      'referer': referer,
      'priority': priority,
      'label': label,
    };
  }

  /// Create VideoSource from map
  factory VideoSource.fromMap(Map<String, dynamic> map) {
    return VideoSource(
      url: map['url'] ?? '',
      userAgent: map['userAgent'],
      referer: map['referer'],
      priority: map['priority'] ?? 0,
      label: map['label'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VideoSource &&
        other.url == url &&
        other.userAgent == userAgent &&
        other.referer == referer &&
        other.priority == priority &&
        other.label == label;
  }

  @override
  int get hashCode {
    return url.hashCode ^
        userAgent.hashCode ^
        referer.hashCode ^
        priority.hashCode ^
        label.hashCode;
  }

  @override
  String toString() {
    return 'VideoSource(url: $url, userAgent: $userAgent, referer: $referer, priority: $priority, label: $label)';
  }

  /// Check if this source has valid URL
  bool get isValid => url.isNotEmpty && Uri.tryParse(url) != null;

  /// Get display name for this source
  String get displayName {
    if (label != null && label!.isNotEmpty) {
      return label!;
    }
    return 'Source ${priority + 1}';
  }
}
